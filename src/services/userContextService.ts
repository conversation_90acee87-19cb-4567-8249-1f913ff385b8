import { supabase } from '@/integrations/supabase/client';
import { User } from '@supabase/supabase-js';

export interface UserProfile {
  id: string;
  full_name?: string;
  phone?: string;
  email?: string;
  role?: string;
  created_at?: string;
  last_login?: string;
}

export interface RecentBooking {
  id: string;
  venue_name: string;
  court_name: string;
  sport_name: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  status: string;
  total_amount: number;
  created_at: string;
}

export interface HelpRequestSummary {
  id: string;
  ticket_number: string;
  subject: string;
  status: string;
  category: string;
  created_at: string;
  last_message_at: string;
}

export interface PageContext {
  path: string;
  page_type: 'home' | 'venues' | 'venue_details' | 'booking' | 'profile' | 'bookings' | 'other';
  venue_id?: string;
  venue_name?: string;
  sport_filter?: string;
  location_filter?: string;
}

export interface SessionContext {
  session_id: string;
  user_agent: string;
  device_type: 'mobile' | 'tablet' | 'desktop';
  browser: string;
  location?: {
    city?: string;
    state?: string;
    country?: string;
  };
  referrer?: string;
  session_duration: number;
}

export interface ComprehensiveUserContext {
  user: UserProfile;
  recentBookings: RecentBooking[];
  helpRequestHistory: HelpRequestSummary[];
  pageContext: PageContext;
  sessionContext: SessionContext;
  preferences: {
    preferred_sports?: string[];
    preferred_locations?: string[];
    notification_preferences?: any;
  };
  supportMetadata: {
    total_bookings: number;
    total_help_requests: number;
    last_support_interaction?: string;
    common_issues?: string[];
    satisfaction_rating?: number;
  };
}

class UserContextService {
  /**
   * Get comprehensive user context for support
   */
  async getUserContext(user: User): Promise<ComprehensiveUserContext> {
    const [
      userProfile,
      recentBookings,
      helpRequestHistory,
      supportMetadata
    ] = await Promise.all([
      this.getUserProfile(user),
      this.getRecentBookings(user.id),
      this.getHelpRequestHistory(user.id),
      this.getSupportMetadata(user.id)
    ]);

    return {
      user: userProfile,
      recentBookings,
      helpRequestHistory,
      pageContext: this.getPageContext(),
      sessionContext: this.getSessionContext(),
      preferences: await this.getUserPreferences(user.id),
      supportMetadata
    };
  }

  /**
   * Get user profile information
   */
  private async getUserProfile(user: User): Promise<UserProfile> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, phone, created_at')
        .eq('id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found error
        console.error('Error fetching user profile:', error);
      }

      return {
        id: user.id,
        full_name: data?.full_name || user.user_metadata?.full_name,
        phone: data?.phone || user.phone,
        email: user.email,
        role: user.user_metadata?.role || 'user',
        created_at: data?.created_at || user.created_at,
        last_login: user.last_sign_in_at
      };
    } catch (error) {
      console.error('Error in getUserProfile:', error);
      return {
        id: user.id,
        email: user.email,
        role: 'user'
      };
    }
  }

  /**
   * Get recent bookings (last 10)
   */
  private async getRecentBookings(userId: string): Promise<RecentBooking[]> {
    try {
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          id,
          booking_date,
          start_time,
          end_time,
          status,
          total_amount,
          created_at,
          court:courts(
            name,
            venue:venues(name),
            sport:sports(name)
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;

      return (data || []).map(booking => ({
        id: booking.id,
        venue_name: booking.court?.venue?.name || 'Unknown Venue',
        court_name: booking.court?.name || 'Unknown Court',
        sport_name: booking.court?.sport?.name || 'Unknown Sport',
        booking_date: booking.booking_date,
        start_time: booking.start_time,
        end_time: booking.end_time,
        status: booking.status,
        total_amount: booking.total_amount,
        created_at: booking.created_at
      }));
    } catch (error) {
      console.error('Error fetching recent bookings:', error);
      return [];
    }
  }

  /**
   * Get help request history (last 5)
   */
  private async getHelpRequestHistory(userId: string): Promise<HelpRequestSummary[]> {
    try {
      const { data, error } = await supabase
        .from('help_requests')
        .select('id, ticket_number, subject, status, category, created_at, last_message_at')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error fetching help request history:', error);
      return [];
    }
  }

  /**
   * Get current page context
   */
  private getPageContext(): PageContext {
    const path = window.location.pathname;
    const searchParams = new URLSearchParams(window.location.search);
    
    let pageType: PageContext['page_type'] = 'other';
    let venueId: string | undefined;
    let venueName: string | undefined;

    // Determine page type and extract relevant data
    if (path === '/') {
      pageType = 'home';
    } else if (path === '/venues') {
      pageType = 'venues';
    } else if (path.startsWith('/venues/')) {
      pageType = 'venue_details';
      venueId = path.split('/')[2];
      // Try to get venue name from page title or DOM
      const titleElement = document.querySelector('h1');
      venueName = titleElement?.textContent || undefined;
    } else if (path === '/bookings') {
      pageType = 'bookings';
    } else if (path === '/profile') {
      pageType = 'profile';
    } else if (path.includes('/booking')) {
      pageType = 'booking';
    }

    return {
      path,
      page_type: pageType,
      venue_id: venueId,
      venue_name: venueName,
      sport_filter: searchParams.get('sport') || undefined,
      location_filter: searchParams.get('location') || undefined
    };
  }

  /**
   * Get session context
   */
  private getSessionContext(): SessionContext {
    const userAgent = navigator.userAgent;
    const sessionStart = sessionStorage.getItem('session_start');
    const sessionId = sessionStorage.getItem('session_id') || `session_${Date.now()}`;
    
    // Store session ID if not exists
    if (!sessionStorage.getItem('session_id')) {
      sessionStorage.setItem('session_id', sessionId);
    }
    
    // Store session start time if not exists
    if (!sessionStart) {
      sessionStorage.setItem('session_start', Date.now().toString());
    }

    const sessionDuration = sessionStart 
      ? Date.now() - parseInt(sessionStart)
      : 0;

    return {
      session_id: sessionId,
      user_agent: userAgent,
      device_type: this.getDeviceType(),
      browser: this.getBrowser(),
      referrer: document.referrer || undefined,
      session_duration: sessionDuration
    };
  }

  /**
   * Get user preferences
   */
  private async getUserPreferences(userId: string): Promise<any> {
    try {
      // This could be extended to fetch from a user_preferences table
      const preferences = localStorage.getItem(`user_preferences_${userId}`);
      return preferences ? JSON.parse(preferences) : {};
    } catch (error) {
      console.error('Error getting user preferences:', error);
      return {};
    }
  }

  /**
   * Get support metadata
   */
  private async getSupportMetadata(userId: string): Promise<any> {
    try {
      const [bookingCount, helpRequestCount] = await Promise.all([
        this.getTotalBookings(userId),
        this.getTotalHelpRequests(userId)
      ]);

      return {
        total_bookings: bookingCount,
        total_help_requests: helpRequestCount,
        last_support_interaction: await this.getLastSupportInteraction(userId)
      };
    } catch (error) {
      console.error('Error getting support metadata:', error);
      return {
        total_bookings: 0,
        total_help_requests: 0
      };
    }
  }

  /**
   * Helper methods
   */
  private async getTotalBookings(userId: string): Promise<number> {
    const { count, error } = await supabase
      .from('bookings')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);
    
    return error ? 0 : (count || 0);
  }

  private async getTotalHelpRequests(userId: string): Promise<number> {
    const { count, error } = await supabase
      .from('help_requests')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);
    
    return error ? 0 : (count || 0);
  }

  private async getLastSupportInteraction(userId: string): Promise<string | undefined> {
    const { data, error } = await supabase
      .from('help_requests')
      .select('last_message_at')
      .eq('user_id', userId)
      .order('last_message_at', { ascending: false })
      .limit(1)
      .single();

    return error ? undefined : data?.last_message_at;
  }

  private getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
    const width = window.innerWidth;
    if (width < 768) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
  }

  private getBrowser(): string {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  }

  /**
   * Format context for MSG91 integration
   */
  formatForMSG91(context: ComprehensiveUserContext): any {
    return {
      // Basic user info
      unique_id: context.user.id,
      name: context.user.full_name || context.user.email?.split('@')[0] || 'Grid२Play User',
      number: context.user.phone || '',
      mail: context.user.email || '',
      country: 'India',
      
      // Enhanced context data
      custom_data: {
        // User profile
        user_role: context.user.role,
        member_since: context.user.created_at,
        last_login: context.user.last_login,
        
        // Activity summary
        total_bookings: context.supportMetadata.total_bookings,
        total_help_requests: context.supportMetadata.total_help_requests,
        last_support_interaction: context.supportMetadata.last_support_interaction,
        
        // Current session
        current_page: context.pageContext.path,
        page_type: context.pageContext.page_type,
        venue_context: context.pageContext.venue_id ? {
          venue_id: context.pageContext.venue_id,
          venue_name: context.pageContext.venue_name
        } : null,
        
        // Recent activity
        recent_bookings: context.recentBookings.slice(0, 3).map(booking => ({
          venue: booking.venue_name,
          sport: booking.sport_name,
          date: booking.booking_date,
          status: booking.status
        })),
        
        // Device info
        device_type: context.sessionContext.device_type,
        browser: context.sessionContext.browser,
        session_duration: Math.round(context.sessionContext.session_duration / 1000 / 60), // minutes
        
        // Support context
        support_escalation: true,
        escalation_timestamp: new Date().toISOString()
      }
    };
  }
}

// Export singleton instance
export const userContextService = new UserContextService();
export default userContextService;
