import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { toast } from '@/components/ui/use-toast';
import { faqService, type FAQ } from '@/services/faqService';
import { userContextService } from '@/services/userContextService';

// Extend window object for MSG91
declare global {
  interface Window {
    initChatWidget: (config: any, delay?: number) => void;
  }
}
import { 
  MessageCircle, 
  X, 
  Search, 
  HelpCircle, 
  Send, 
  ArrowLeft, 
  ExternalLink,
  Loader2,
  Phone,
  Mail,
  Clock,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';


// Security utilities
class InputValidator {
  static sanitizeSearchQuery(query: string): string {
    // Remove potentially dangerous characters and limit length
    return query
      .replace(/[<>"'&;-]/g, '') // Remove HTML/script/SQL chars
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()
      .substring(0, 100); // Limit length
  }

  static validateTicketInput(subject: string, message: string): { isValid: boolean; error?: string } {
    const subjectRegex = /^[a-zA-Z0-9\s\-.,!?]{3,100}$/;
    const messageRegex = /^[a-zA-Z0-9\s\-.,!?\n]{10,1000}$/;

    if (!subjectRegex.test(subject)) {
      return { isValid: false, error: 'Subject must be 3-100 characters and contain only letters, numbers, and basic punctuation' };
    }

    if (!messageRegex.test(message)) {
      return { isValid: false, error: 'Message must be 10-1000 characters and contain only letters, numbers, and basic punctuation' };
    }

    return { isValid: true };
  }
}

// Rate limiting utility
class RateLimiter {
  private static attempts: Map<string, number[]> = new Map();

  static checkLimit(userId: string, action: string, maxAttempts: number = 10): boolean {
    const key = `${userId}_${action}`;
    const now = Date.now();
    const windowMs = 60000; // 1 minute window

    const userAttempts = this.attempts.get(key) || [];
    const recentAttempts = userAttempts.filter(time => now - time < windowMs);

    if (recentAttempts.length >= maxAttempts) {
      return false; // Rate limit exceeded
    }

    recentAttempts.push(now);
    this.attempts.set(key, recentAttempts);
    return true;
  }
}

// FAQ interface is now imported from faqService

interface EnhancedMSG91ChatWidgetProps {
  className?: string;
  isOpen?: boolean;
  setIsOpen?: (open: boolean) => void;
}

type SupportStep = 'launcher' | 'faq-search' | 'faq-detail' | 'help-options' | 'msg91-chat' | 'ticket-form';

export const EnhancedMSG91ChatWidget: React.FC<EnhancedMSG91ChatWidgetProps> = ({
  className,
  isOpen: isOpenProp,
  setIsOpen: setIsOpenProp
}) => {
  const { user } = useAuth();
  const [isOpenState, setIsOpenState] = useState(false);

  // Use external state if provided, otherwise use internal state
  const isOpen = typeof isOpenProp === 'boolean' ? isOpenProp : isOpenState;
  const setIsOpen = setIsOpenProp || setIsOpenState;
  const [currentStep, setCurrentStep] = useState<SupportStep>('faq-search');
  const [searchQuery, setSearchQuery] = useState('');
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [filteredFaqs, setFilteredFaqs] = useState<FAQ[]>([]);
  const [loading, setLoading] = useState(false);
  const [msg91Initialized, setMsg91Initialized] = useState(false);
  const [selectedFAQ, setSelectedFAQ] = useState<FAQ | null>(null);
  const [msg91ScriptLoaded, setMsg91ScriptLoaded] = useState(false);
  const [msg91Error, setMsg91Error] = useState<string | null>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const msg91TimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Load FAQs on component mount using the service
  useEffect(() => {
    const loadFAQs = async () => {
      try {
        const faqData = await faqService.getGeneralFAQs();
        setFaqs(faqData);
        setFilteredFaqs(faqData);
      } catch (error) {
        console.error('Error loading FAQs:', error);
      }
    };

    loadFAQs();
  }, []);

  // Filter FAQs based on search query using the service with security
  useEffect(() => {
    const searchFAQs = async () => {
      if (!searchQuery.trim()) {
        setFilteredFaqs(faqs);
        return;
      }

      // Rate limiting check
      if (!RateLimiter.checkLimit(user?.id || 'anonymous', 'faq_search', 20)) {
        toast({
          title: "Too Many Searches",
          description: "Please wait a moment before searching again.",
          variant: "destructive"
        });
        return;
      }

      try {
        // Sanitize search input
        const sanitizedQuery = InputValidator.sanitizeSearchQuery(searchQuery);
        if (sanitizedQuery.length < 2) {
          setFilteredFaqs(faqs);
          return;
        }

        const searchResults = await faqService.searchFAQs(sanitizedQuery);
        const filteredResults = searchResults
          .filter(result => result.type === 'general')
          .map(result => result.faq as FAQ);
        setFilteredFaqs(filteredResults);
      } catch (error) {
        console.error('Error searching FAQs:', error);
        // Fallback to simple filtering with sanitized input
        const sanitizedQuery = InputValidator.sanitizeSearchQuery(searchQuery);
        const filtered = faqs.filter(faq =>
          faq.question.toLowerCase().includes(sanitizedQuery.toLowerCase()) ||
          faq.answer.toLowerCase().includes(sanitizedQuery.toLowerCase()) ||
          faq.tags.some(tag => tag.toLowerCase().includes(sanitizedQuery.toLowerCase()))
        );
        setFilteredFaqs(filtered);
      }
    };

    // Debounce search to prevent excessive API calls
    const timeoutId = setTimeout(searchFAQs, 300);
    return () => clearTimeout(timeoutId);
  }, [searchQuery, faqs, user?.id]);

  // Clean up MSG91 widget and reset state
  const cleanupMSG91 = () => {
    try {
      // Clear any pending timeouts
      if (msg91TimeoutRef.current) {
        clearTimeout(msg91TimeoutRef.current);
        msg91TimeoutRef.current = null;
      }

      // Remove MSG91 widget elements
      const widgets = document.querySelectorAll('[id*="msg91"], [class*="msg91"], [class*="chat-widget"], [id*="hello-widget"]');
      widgets.forEach(widget => widget.remove());

      // Reset initialization state
      setMsg91Initialized(false);
      setMsg91Error(null);
      setCurrentStep('faq-search');
      setLoading(false);

      console.log('MSG91 widget cleaned up successfully');
    } catch (error) {
      console.error('Error cleaning up MSG91 widget:', error);
    }
  };

  // Initialize MSG91 chat widget with enhanced user context
  const initializeMSG91 = async () => {
    if (msg91Initialized || !user) return;

    setLoading(true);

    // Set a timeout to prevent infinite loading
    msg91TimeoutRef.current = setTimeout(() => {
      if (loading) {
        setLoading(false);
        setMsg91Error('Connection timeout. Please try again.');
        setCurrentStep('faq-search');
      }
    }, 15000); // 15 second timeout

    try {
      // Clean up any existing widgets first
      cleanupMSG91();

      // Get comprehensive user context with fallback
      let msg91Config;
      try {
        const userContext = await userContextService.getUserContext(user);
        msg91Config = userContextService.formatForMSG91(userContext);
      } catch (error) {
        console.error('Error getting user context for MSG91:', error);
        // Fallback to minimal configuration
        msg91Config = {
          unique_id: user.id || `guest_${Date.now()}`,
          name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Grid२Play User',
          number: user.phone || '',
          mail: user.email || '',
          country: 'India'
        };
      }

      // Check if script is already loaded
      const existingScript = document.querySelector('script[src*="chat-widget.js"]');

      if (existingScript && msg91ScriptLoaded) {
        // Script already loaded, just initialize
        const finalConfig = {
          ...msg91Config,
          widgetToken: "65828",
          hide_launcher: true,
          show_widget_form: false,
          show_close_button: true,
          launch_widget: true,
          show_send_button: true,
        };

        if (typeof window.initChatWidget === 'function') {
          window.initChatWidget(finalConfig, 500);
          setMsg91Initialized(true);
          setCurrentStep('msg91-chat');

          // Clear timeout
          if (msg91TimeoutRef.current) {
            clearTimeout(msg91TimeoutRef.current);
            msg91TimeoutRef.current = null;
          }

          // Hide our widget after a delay to let MSG91 take over
          setTimeout(() => {
            setIsOpen(false);
            setLoading(false);
          }, 1000);
        }
        return;
      }

      // Load MSG91 script
      const script = document.createElement('script');
      script.src = 'https://blacksea.msg91.com/chat-widget.js';
      script.async = true;

      script.onload = () => {
        setMsg91ScriptLoaded(true);

        const finalConfig = {
          ...msg91Config,
          widgetToken: "65828",
          hide_launcher: true,
          show_widget_form: false,
          show_close_button: true,
          launch_widget: true,
          show_send_button: true,
        };

        if (typeof window.initChatWidget === 'function') {
          window.initChatWidget(finalConfig, 500);
          setMsg91Initialized(true);
          setCurrentStep('msg91-chat');

          // Clear timeout
          if (msg91TimeoutRef.current) {
            clearTimeout(msg91TimeoutRef.current);
            msg91TimeoutRef.current = null;
          }

          // Hide our widget after a delay to let MSG91 take over
          setTimeout(() => {
            setIsOpen(false);
            setLoading(false);
          }, 1000);
        }
      };

      script.onerror = () => {
        console.error('Failed to load MSG91 script');
        setLoading(false);
        setMsg91Error('Failed to load live chat. Please check your internet connection.');
        toast({
          title: "Connection Error",
          description: "Unable to connect to live chat. Please try again or create a help ticket.",
          variant: "destructive"
        });
        setCurrentStep('faq-search');
      };

      document.head.appendChild(script);
    } catch (error) {
      console.error('Error initializing MSG91 with user context:', error);
      setLoading(false);

      // Fallback to basic initialization
      try {
        const script = document.createElement('script');
        script.src = 'https://blacksea.msg91.com/chat-widget.js';
        script.async = true;

        script.onload = () => {
          const basicConfig = {
            widgetToken: "65828",
            hide_launcher: true,
            show_widget_form: false,
            show_close_button: true,
            launch_widget: true,
            show_send_button: true,
            unique_id: user?.id || `guest_${Date.now()}`,
            name: user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'Grid२Play User',
            number: user?.phone || '',
            mail: user?.email || '',
            country: 'India'
          };

          if (typeof window.initChatWidget === 'function') {
            window.initChatWidget(basicConfig, 500);
            setMsg91Initialized(true);
            setCurrentStep('msg91-chat');

            // Clear timeout
            if (msg91TimeoutRef.current) {
              clearTimeout(msg91TimeoutRef.current);
              msg91TimeoutRef.current = null;
            }

            setTimeout(() => {
              setIsOpen(false);
              setLoading(false);
            }, 1000);
          }
        };

        document.head.appendChild(script);
      } catch (fallbackError) {
        console.error('Fallback MSG91 initialization failed:', fallbackError);
        toast({
          title: "Live Chat Unavailable",
          description: "Live chat is temporarily unavailable. Please create a help ticket instead.",
          variant: "destructive"
        });
        setCurrentStep('faq-search');
        setLoading(false);
      }
    }
  };

  // Handle FAQ selection
  const handleFAQSelect = (faq: FAQ) => {
    setSelectedFAQ(faq);
    setCurrentStep('faq-detail');
  };

  // Handle escalation to MSG91 live chat
  const handleEscalateToLiveChat = () => {
    if (!user) {
      toast({
        title: "Sign In Required",
        description: "Please sign in to access live chat support",
        variant: "destructive"
      });
      return;
    }

    // Clear any previous errors
    setMsg91Error(null);
    initializeMSG91();
  };

  // Retry MSG91 initialization
  const retryMSG91 = () => {
    setMsg91Error(null);
    setMsg91Initialized(false);
    setMsg91ScriptLoaded(false);
    initializeMSG91();
  };

  // Handle help ticket creation
  const handleCreateTicket = () => {
    if (!user) {
      toast({
        title: "Sign In Required", 
        description: "Please sign in to create a help ticket",
        variant: "destructive"
      });
      return;
    }
    setCurrentStep('ticket-form');
  };

  // Handle widget close with proper cleanup
  const handleCloseWidget = () => {
    // If MSG91 is initialized, clean it up
    if (msg91Initialized) {
      cleanupMSG91();
    }

    // Reset widget state
    setCurrentStep('faq-search');
    setSelectedFAQ(null);
    setSearchQuery('');
    setIsOpen(false);
  };

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      if (msg91Initialized) {
        cleanupMSG91();
      }
    };
  }, [msg91Initialized]);

  const quickActions = [
    { label: 'Booking Issues', query: 'booking cancel refund' },
    { label: 'Payment Problems', query: 'payment card upi' },
    { label: 'Venue Questions', query: 'venue facility location' },
    { label: 'Account Help', query: 'account profile login' }
  ];

  // Don't render widget if user is not authenticated
  if (!user) {
    return null;
  }

  return (
    <>
      {/* Launcher Button */}
      <AnimatePresence>
        {!isOpen && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            className={cn(
              "fixed right-6 z-50",
              // Mobile: position above bottom nav, Desktop: standard position
              "bottom-20 md:bottom-6",
              className
            )}
          >
            <Button
              onClick={() => setIsOpen(true)}
              className={cn(
                "w-14 h-14 rounded-full shadow-lg",
                "bg-emerald-900 hover:bg-emerald-800",
                "text-white border-2 border-emerald-700",
                "transition-all duration-300 hover:scale-110",
                "focus:ring-4 focus:ring-emerald-900/20"
              )}
              aria-label="Get Help"
            >
              <MessageCircle className="w-6 h-6" />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Chat Widget */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            className={cn(
              "fixed right-6 z-50",
              // Mobile: position above bottom nav, Desktop: standard position
              "bottom-20 md:bottom-6",
              "w-96 h-[600px] max-w-[calc(100vw-2rem)] max-h-[calc(100vh-2rem)]",
              "bg-black border border-emerald-900/30 rounded-2xl shadow-2xl",
              "flex flex-col overflow-hidden",
              "md:w-96 md:h-[600px]",
              className
            )}
          >
            {/* Header */}
            <div className="bg-emerald-900 text-white p-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-emerald-800 rounded-full flex items-center justify-center">
                  <HelpCircle className="w-4 h-4" />
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Grid२Play Support</h3>
                  <p className="text-emerald-200 text-xs">How can we help you?</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCloseWidget}
                className="text-white hover:bg-emerald-800 w-8 h-8 p-0"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Content Area */}
            <div className="flex-1 flex flex-col bg-gray-900">
              {currentStep === 'faq-search' && (
                <div className="flex-1 flex flex-col p-4">
                  {/* Search Bar */}
                  <div className="relative mb-4">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      ref={searchInputRef}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder="Search for help..."
                      className={cn(
                        "pl-10 bg-gray-800 border-gray-700 text-white",
                        "focus:border-emerald-500 focus:ring-emerald-500/20",
                        "min-h-[44px]" // Mobile touch target
                      )}
                    />
                  </div>

                  {/* Quick Actions */}
                  <div className="mb-4">
                    <p className="text-gray-400 text-xs mb-2">Quick Help:</p>
                    <div className="grid grid-cols-2 gap-2">
                      {quickActions.map((action, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => setSearchQuery(action.query)}
                          className={cn(
                            "text-xs border-gray-700 text-gray-300 hover:bg-emerald-900/20",
                            "hover:border-emerald-700 hover:text-emerald-300",
                            "min-h-[44px] p-2" // Mobile touch target
                          )}
                        >
                          {action.label}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* FAQ Results */}
                  <ScrollArea className="flex-1">
                    <div className="space-y-2">
                      {filteredFaqs.length > 0 ? (
                        filteredFaqs.map((faq) => (
                          <div
                            key={faq.id}
                            onClick={() => handleFAQSelect(faq)}
                            className={cn(
                              "p-3 bg-gray-800 rounded-lg cursor-pointer",
                              "hover:bg-gray-700 transition-colors",
                              "border border-gray-700 hover:border-emerald-700",
                              "min-h-[44px]" // Mobile touch target
                            )}
                          >
                            <h4 className="text-white text-sm font-medium mb-1">
                              {faq.question}
                            </h4>
                            <p className="text-gray-400 text-xs line-clamp-2">
                              {faq.answer}
                            </p>
                            {faq.tags.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-2">
                                {faq.tags.slice(0, 3).map((tag, index) => (
                                  <Badge
                                    key={index}
                                    variant="secondary"
                                    className="text-xs bg-emerald-900/20 text-emerald-300"
                                  >
                                    {tag}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <HelpCircle className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                          <p className="text-gray-400 text-sm mb-4">
                            No FAQs found for "{searchQuery}"
                          </p>
                          <p className="text-gray-500 text-xs">
                            Try different keywords or get personalized help below
                          </p>
                        </div>
                      )}
                    </div>
                  </ScrollArea>

                  {/* Action Buttons */}
                  <div className="mt-4 space-y-2">
                    <Button
                      onClick={handleEscalateToLiveChat}
                      disabled={loading}
                      className={cn(
                        "w-full bg-emerald-900 hover:bg-emerald-800",
                        "text-white min-h-[44px]" // Mobile touch target
                      )}
                    >
                      {loading ? (
                        <Loader2 className="w-4 h-4 animate-spin mr-2" />
                      ) : (
                        <MessageCircle className="w-4 h-4 mr-2" />
                      )}
                      Chat with Live Support
                    </Button>
                    
                    <Button
                      onClick={handleCreateTicket}
                      variant="outline"
                      className={cn(
                        "w-full border-gray-700 text-gray-300",
                        "hover:bg-gray-800 hover:border-emerald-700",
                        "min-h-[44px]" // Mobile touch target
                      )}
                    >
                      <HelpCircle className="w-4 h-4 mr-2" />
                      Create Help Ticket
                    </Button>
                  </div>
                </div>
              )}

              {/* FAQ Detail Step */}
              {currentStep === 'faq-detail' && selectedFAQ && (
                <FAQDetail
                  faq={selectedFAQ}
                  onBack={() => setCurrentStep('faq-search')}
                  onEscalateToChat={handleEscalateToLiveChat}
                  onCreateTicket={handleCreateTicket}
                />
              )}

              {/* Ticket Form Step */}
              {currentStep === 'ticket-form' && (
                <TicketForm
                  onBack={() => setCurrentStep('faq-search')}
                  onSuccess={() => {
                    setCurrentStep('faq-search');
                    setIsOpen(false);
                  }}
                />
              )}

              {/* MSG91 Chat Step */}
              {currentStep === 'msg91-chat' && (
                <div className="flex-1 flex flex-col items-center justify-center p-6">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-emerald-900 rounded-full flex items-center justify-center mx-auto mb-4">
                      <MessageCircle className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-white font-semibold mb-2">
                      {msg91Error
                        ? 'Connection Failed'
                        : loading
                          ? 'Connecting to Live Support'
                          : 'Live Chat Ready'
                      }
                    </h3>
                    <p className="text-gray-400 text-sm mb-4">
                      {msg91Error
                        ? msg91Error
                        : loading
                          ? 'Setting up your chat session with full context...'
                          : 'Live chat window will open shortly. You can close this widget.'
                      }
                    </p>
                    <div className="flex items-center justify-center mb-4">
                      {msg91Error ? (
                        <X className="w-5 h-5 text-red-500" />
                      ) : loading ? (
                        <Loader2 className="w-5 h-5 animate-spin text-emerald-500" />
                      ) : (
                        <CheckCircle className="w-5 h-5 text-emerald-500" />
                      )}
                    </div>

                    {msg91Error && (
                      <div className="space-y-2">
                        <Button
                          onClick={retryMSG91}
                          className="w-full bg-emerald-900 hover:bg-emerald-800 text-white min-h-[44px]"
                        >
                          Retry Live Chat
                        </Button>
                        <Button
                          onClick={() => setCurrentStep('faq-search')}
                          variant="outline"
                          className="w-full border-gray-700 text-gray-300 hover:bg-gray-800 min-h-[44px]"
                        >
                          Back to FAQ Search
                        </Button>
                      </div>
                    )}

                    {!loading && !msg91Error && (
                      <div className="space-y-2">
                        <Button
                          onClick={handleCloseWidget}
                          className="bg-emerald-900 hover:bg-emerald-800 text-white min-h-[44px]"
                        >
                          Close Widget
                        </Button>
                        <Button
                          onClick={() => setCurrentStep('faq-search')}
                          variant="outline"
                          className="w-full border-gray-700 text-gray-300 hover:bg-gray-800 min-h-[44px]"
                        >
                          Back to FAQ Search
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

// Ticket Form Component
interface TicketFormProps {
  onBack: () => void;
  onSuccess: () => void;
}

const TicketForm: React.FC<TicketFormProps> = ({ onBack, onSuccess }) => {
  const { user } = useAuth();
  const [subject, setSubject] = useState('');
  const [category, setCategory] = useState('general');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);

  const categories = [
    { value: 'booking_issues', label: 'Booking Issues' },
    { value: 'payment_problems', label: 'Payment Problems' },
    { value: 'facility_questions', label: 'Facility Questions' },
    { value: 'general', label: 'General Support' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || !subject.trim() || !message.trim()) return;

    // Rate limiting check
    if (!RateLimiter.checkLimit(user.id, 'ticket_creation', 3)) {
      toast({
        title: "Too Many Requests",
        description: "Please wait before creating another ticket.",
        variant: "destructive"
      });
      return;
    }

    // Input validation
    const validation = InputValidator.validateTicketInput(subject.trim(), message.trim());
    if (!validation.isValid) {
      toast({
        title: "Invalid Input",
        description: validation.error,
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      // Sanitize inputs
      const sanitizedSubject = subject.trim().substring(0, 100);
      const sanitizedMessage = message.trim().substring(0, 1000);

      const { data, error } = await supabase.rpc('create_help_request', {
        p_user_id: user.id,
        p_subject: sanitizedSubject,
        p_venue_id: null,
        p_category: category
      });

      if (error) throw error;

      // Send initial message with sanitized content
      await supabase.from('messages').insert({
        content: sanitizedMessage,
        user_id: user.id,
        sender_id: user.id,
        venue_id: null,
        is_read: false
      });

      toast({
        title: 'Help Request Created',
        description: `Your ticket has been submitted. We'll respond soon!`,
      });

      onSuccess();
    } catch (error) {
      console.error('Error creating help request:', error);
      toast({
        title: 'Error',
        description: 'Failed to create help request. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex-1 flex flex-col p-4">
      <div className="flex items-center gap-3 mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onBack}
          className="text-gray-400 hover:text-white p-1"
        >
          <ArrowLeft className="w-4 h-4" />
        </Button>
        <h3 className="text-white font-semibold">Create Help Ticket</h3>
      </div>

      <form onSubmit={handleSubmit} className="flex-1 flex flex-col space-y-4">
        <div>
          <label className="text-gray-300 text-sm mb-2 block">Subject</label>
          <Input
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
            placeholder="Brief description of your issue"
            className={cn(
              "bg-gray-800 border-gray-700 text-white",
              "focus:border-emerald-500 focus:ring-emerald-500/20",
              "min-h-[44px]"
            )}
            required
          />
        </div>

        <div>
          <label className="text-gray-300 text-sm mb-2 block">Category</label>
          <select
            value={category}
            onChange={(e) => setCategory(e.target.value)}
            className={cn(
              "w-full bg-gray-800 border border-gray-700 text-white rounded-md px-3 py-2",
              "focus:border-emerald-500 focus:ring-emerald-500/20 focus:outline-none",
              "min-h-[44px]"
            )}
          >
            {categories.map((cat) => (
              <option key={cat.value} value={cat.value}>
                {cat.label}
              </option>
            ))}
          </select>
        </div>

        <div className="flex-1">
          <label className="text-gray-300 text-sm mb-2 block">Message</label>
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Please describe your issue in detail..."
            className={cn(
              "w-full bg-gray-800 border border-gray-700 text-white rounded-md px-3 py-2",
              "focus:border-emerald-500 focus:ring-emerald-500/20 focus:outline-none",
              "resize-none min-h-[120px]"
            )}
            required
          />
        </div>

        <Button
          type="submit"
          disabled={loading || !subject.trim() || !message.trim()}
          className={cn(
            "w-full bg-emerald-900 hover:bg-emerald-800",
            "text-white min-h-[44px]"
          )}
        >
          {loading ? (
            <Loader2 className="w-4 h-4 animate-spin mr-2" />
          ) : (
            <Send className="w-4 h-4 mr-2" />
          )}
          Submit Ticket
        </Button>
      </form>
    </div>
  );
};

// FAQ Detail Component
interface FAQDetailProps {
  faq: FAQ;
  onBack: () => void;
  onEscalateToChat: () => void;
  onCreateTicket: () => void;
}

const FAQDetail: React.FC<FAQDetailProps> = ({
  faq,
  onBack,
  onEscalateToChat,
  onCreateTicket
}) => {
  const [wasHelpful, setWasHelpful] = useState<boolean | null>(null);

  const handleFeedback = (helpful: boolean) => {
    setWasHelpful(helpful);
    toast({
      title: helpful ? "Thank you!" : "We'll improve this",
      description: helpful
        ? "Glad we could help you!"
        : "Your feedback helps us improve our FAQs.",
    });
  };

  return (
    <div className="flex-1 flex flex-col p-4">
      <div className="flex items-center gap-3 mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onBack}
          className="text-gray-400 hover:text-white p-1"
        >
          <ArrowLeft className="w-4 h-4" />
        </Button>
        <h3 className="text-white font-semibold">FAQ Answer</h3>
      </div>

      <ScrollArea className="flex-1 mb-4">
        <div className="space-y-4">
          <div>
            <h4 className="text-white font-medium mb-3 leading-relaxed">
              {faq.question}
            </h4>
            <div className="text-gray-300 text-sm leading-relaxed whitespace-pre-wrap">
              {faq.answer}
            </div>
          </div>

          {faq.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {faq.tags.map((tag, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="text-xs bg-emerald-900/20 text-emerald-300 border-emerald-700/30"
                >
                  {tag}
                </Badge>
              ))}
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Feedback Section */}
      <div className="border-t border-gray-700 pt-4 mb-4">
        <p className="text-gray-400 text-sm mb-3">Was this helpful?</p>
        <div className="flex gap-2 mb-4">
          <Button
            variant={wasHelpful === true ? "default" : "outline"}
            size="sm"
            onClick={() => handleFeedback(true)}
            className={cn(
              "flex-1 min-h-[44px]",
              wasHelpful === true
                ? "bg-emerald-900 hover:bg-emerald-800 text-white"
                : "border-gray-700 text-gray-300 hover:bg-gray-800"
            )}
          >
            <CheckCircle className="w-4 h-4 mr-2" />
            Yes, helpful
          </Button>
          <Button
            variant={wasHelpful === false ? "default" : "outline"}
            size="sm"
            onClick={() => handleFeedback(false)}
            className={cn(
              "flex-1 min-h-[44px]",
              wasHelpful === false
                ? "bg-gray-700 hover:bg-gray-600 text-white"
                : "border-gray-700 text-gray-300 hover:bg-gray-800"
            )}
          >
            <X className="w-4 h-4 mr-2" />
            Not helpful
          </Button>
        </div>
      </div>

      {/* Still need help? */}
      {wasHelpful === false && (
        <div className="space-y-2">
          <p className="text-gray-400 text-sm mb-3">Still need help?</p>
          <Button
            onClick={onEscalateToChat}
            className={cn(
              "w-full bg-emerald-900 hover:bg-emerald-800",
              "text-white min-h-[44px]"
            )}
          >
            <MessageCircle className="w-4 h-4 mr-2" />
            Chat with Live Support
          </Button>

          <Button
            onClick={onCreateTicket}
            variant="outline"
            className={cn(
              "w-full border-gray-700 text-gray-300",
              "hover:bg-gray-800 hover:border-emerald-700",
              "min-h-[44px]"
            )}
          >
            <HelpCircle className="w-4 h-4 mr-2" />
            Create Help Ticket
          </Button>
        </div>
      )}
    </div>
  );
};

export default EnhancedMSG91ChatWidget;
